#!/usr/bin/env elixir

# Test script to verify the SchemaCache fix works with the actual folders.json file

Mix.install([
  {:jason, "~> 1.4"}
])

defmodule TestSchemaCacheFix do
  def run do
    # Read the problematic JSON file
    {:ok, content} = File.read("tmp/folders.json")
    data = Jason.decode!(content)
    
    # Extract the problematic field data
    fields = data["schema"]["fields"]
    source_files_field = Enum.find(fields, &(&1["name"] == "source_files"))
    
    IO.puts("Original field data:")
    IO.inspect(source_files_field, pretty: true)
    
    # Test the specific problematic ecto_type
    problematic_ecto_type = source_files_field["ecto_type"]
    IO.puts("\nProblematic ecto_type: #{inspect(problematic_ecto_type)}")
    
    # This would have crashed before the fix
    IO.puts("Testing if this would crash...")
    
    # Simulate what the deserialize_ecto_type function should do
    result = case problematic_ecto_type do
      %{"array" => element_type} ->
        {:array, String.to_atom(element_type)}
      other ->
        other
    end
    
    IO.puts("Result: #{inspect(result)}")
    IO.puts("✅ No crash! The fix works.")
  end
end

TestSchemaCacheFix.run()

#!/usr/bin/env elixir

# Debug script to check what the introspector returns for binary_id tables

Mix.install([
  {:ecto, "~> 3.10"},
  {:ecto_sql, "~> 3.10"},
  {:ecto_sqlite3, "~> 0.10"}
])

# Load the application
Application.put_env(:drops, :ecto_repos, [])

# Load the Drops modules
Code.require_file("lib/drops/relation/sql/introspector.ex")
Code.require_file("lib/drops/relation/sql/introspector/database.ex")
Code.require_file("lib/drops/relation/sql/introspector/database/sqlite.ex")

defmodule Drops.Repos.Sqlite do
  use Ecto.Repo,
    otp_app: :drops,
    adapter: Ecto.Adapters.SQLite3

  def config do
    [database: "priv/sqlite_test.db"]
  end
end

defmodule DebugIntrospection do
  def run do
    # Start the repo
    {:ok, _} = Drops.Repos.Sqlite.start_link()
    
    alias Drops.Relation.SQL.Introspector
    
    IO.puts("=== Introspecting binary_id_organizations table ===")
    
    # Get columns for binary_id_organizations
    columns = Introspector.introspect_table_columns(Drops.Repos.Sqlite, "binary_id_organizations")
    
    IO.puts("Columns found: #{length(columns)}")
    
    Enum.each(columns, fn column ->
      IO.puts("Column: #{column.name}")
      IO.puts("  DB Type: #{column.type}")
      IO.puts("  Primary Key: #{column.primary_key}")
      IO.puts("  Nullable: #{column.nullable}")
      
      # Test the type conversion
      ecto_type = Introspector.db_type_to_ecto_type(Drops.Repos.Sqlite, column.type, column.name)
      IO.puts("  Ecto Type: #{inspect(ecto_type)}")
      IO.puts("")
    end)
    
    IO.puts("=== Introspecting binary_id_users table ===")
    
    # Get columns for binary_id_users
    columns = Introspector.introspect_table_columns(Drops.Repos.Sqlite, "binary_id_users")
    
    IO.puts("Columns found: #{length(columns)}")
    
    Enum.each(columns, fn column ->
      IO.puts("Column: #{column.name}")
      IO.puts("  DB Type: #{column.type}")
      IO.puts("  Primary Key: #{column.primary_key}")
      IO.puts("  Nullable: #{column.nullable}")
      
      # Test the type conversion
      ecto_type = Introspector.db_type_to_ecto_type(Drops.Repos.Sqlite, column.type, column.name)
      IO.puts("  Ecto Type: #{inspect(ecto_type)}")
      IO.puts("")
    end)
  end
end

DebugIntrospection.run()

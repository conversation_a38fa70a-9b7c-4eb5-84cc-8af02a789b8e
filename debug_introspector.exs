#!/usr/bin/env elixir

# Debug script to check what the introspector returns for binary_id tables

Mix.install([
  {:ecto, "~> 3.10"},
  {:ecto_sql, "~> 3.10"},
  {:ecto_sqlite3, "~> 0.10"}
])

defmodule DebugIntrospector do
  def run do
    # Load the Drops modules
    Code.require_file("lib/drops/relation/sql/introspector.ex")
    Code.require_file("lib/drops/relation/sql/introspector/database.ex")
    Code.require_file("lib/drops/relation/sql/introspector/database/sqlite.ex")
    
    alias Drops.Relation.SQL.Introspector
    alias Drops.Relation.SQL.Introspector.Database.SQLite
    
    # Test the type mapping directly
    IO.puts("Testing SQLite type mapping:")
    IO.puts("TEXT + 'id' -> #{inspect(SQLite.db_type_to_ecto_type("TEXT", "id"))}")
    IO.puts("TEXT + 'organization_id' -> #{inspect(SQLite.db_type_to_ecto_type("TEXT", "organization_id"))}")
    IO.puts("TEXT + 'name' -> #{inspect(SQLite.db_type_to_ecto_type("TEXT", "name"))}")
    IO.puts("INTEGER + 'id' -> #{inspect(SQLite.db_type_to_ecto_type("INTEGER", "id"))}")
  end
end

DebugIntrospector.run()

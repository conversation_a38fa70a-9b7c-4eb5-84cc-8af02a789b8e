defmodule Drops.Relation.BinaryIdIntrospectionTest do
  use Drops.RelationCase, async: false

  alias Drops.Relation.SQL.Introspector
  alias Drops.Relation.SQL.Inference

  describe "binary_id introspection" do
    test "introspector correctly identifies binary_id fields in SQLite" do
      # Test the introspector directly
      columns =
        Introspector.introspect_table_columns(
          Drops.Repos.Sqlite,
          "binary_id_organizations"
        )

      # Find the id column
      id_column = Enum.find(columns, &(&1.name == "id"))
      assert id_column
      assert id_column.primary_key == true
      # SQLite stores binary_id as TEXT
      assert id_column.type == "TEXT"

      # Test the type conversion
      ecto_type =
        Introspector.db_type_to_ecto_type(
          Drops.Repos.Sqlite,
          id_column.type,
          id_column.name
        )

      assert ecto_type == :binary_id

      # Test foreign key field
      user_columns =
        Introspector.introspect_table_columns(Drops.Repos.Sqlite, "binary_id_users")

      org_id_column = Enum.find(user_columns, &(&1.name == "organization_id"))
      assert org_id_column
      assert org_id_column.type == "TEXT"

      org_id_ecto_type =
        Introspector.db_type_to_ecto_type(
          Drops.Repos.Sqlite,
          org_id_column.type,
          org_id_column.name
        )

      assert org_id_ecto_type == :binary_id
    end

    test "schema inference produces correct binary_id types" do
      # Test the full inference pipeline
      schema = Inference.infer_from_table("binary_id_organizations", Drops.Repos.Sqlite)

      # Check primary key
      assert length(schema.primary_key.fields) == 1
      pk_field = hd(schema.primary_key.fields)
      assert pk_field.name == :id
      assert pk_field.ecto_type == :binary_id
      # This should be :binary for binary_id
      assert pk_field.type == :binary

      # Check that the field is in the fields list
      id_field = Enum.find(schema.fields, &(&1.name == :id))
      assert id_field
      assert id_field.ecto_type == :binary_id
      assert id_field.type == :binary
    end

    test "schema inference handles binary_id foreign keys" do
      # Test foreign key inference
      schema = Inference.infer_from_table("binary_id_users", Drops.Repos.Sqlite)

      # Check foreign key field
      org_id_field = Enum.find(schema.fields, &(&1.name == :organization_id))
      assert org_id_field
      assert org_id_field.ecto_type == :binary_id
      assert org_id_field.type == :binary

      # Check primary key
      pk_field = hd(schema.primary_key.fields)
      assert pk_field.ecto_type == :binary_id
      assert pk_field.type == :binary
    end
  end
end
